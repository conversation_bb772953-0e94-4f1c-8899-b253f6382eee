// Chat page functionality
import { createConsumer } from "@rails/actioncable"

(function() {
  console.log('Chat page JavaScript loaded');
  // Get current user info from global config
  console.log('window.chatConfig:', window.chatConfig);
  console.log('window.ENV:', window.ENV);
  let currentUserId = window.chatConfig?.currentUserId;


  if (!currentUserId) {
    console.error('Current user ID not found in window.chatConfig');

    // Fallback to ENV.current_user_id if available
    if (window.ENV?.current_user_id) {
      console.log('Using ENV.current_user_id as fallback');
      window.chatConfig = window.chatConfig || {};
      window.chatConfig.currentUserId = window.ENV.current_user_id;
      currentUserId = window.ENV.current_user_id;
    } else {
      console.error('No current user ID found');
      return;
    }
  }

  // DOM elements - will be set during initialization
  let chatUsersList = null;
  let chatMessages = null;
  let chatInput = null;
  let sendButton = null;
  let chatForm = null;
  let recipientIdInput = null;
  let selectedUserName = null;
  let searchInput = null;

  // State
  let currentRecipientId = null;
  let allUsers = [];
  let chatSubscription = null;
  let unreadCounts = {}; // Track unread message counts per user

  // Initialize the chat
  function initialize() {
    console.log('Initializing chat...');

    // Find DOM elements
    chatUsersList = document.querySelector('.chat-users-list');
    chatMessages = document.getElementById('chat-messages');
    chatInput = document.getElementById('chat-input');
    sendButton = document.getElementById('send-button');
    chatForm = document.getElementById('chat-form');
    recipientIdInput = document.getElementById('recipient-id');
    selectedUserName = document.getElementById('selected-user-name');
    searchInput = document.getElementById('chat-search');

    // Check if required elements exist
    if (!chatUsersList) {
      console.error('Chat users list element not found');
      return;
    }

    if (!chatMessages || !chatInput || !sendButton || !chatForm) {
      console.error('Required chat elements not found');
      return;
    }

    console.log('All required chat elements found, proceeding with initialization...');

    // Test ActionCable availability
    testActionCableConnection();

    // Initial load of user list and setup
    loadInitialUserList();
    setupEventListeners();
    setupPresenceChannel();
    setupPageUnloadHandler();

    // No more polling intervals - everything is real-time through ActionCable!
  }

  // Test ActionCable connection
  function testActionCableConnection() {
    try {
      const cable = createConsumer('/cable');
      console.log('ActionCable consumer created:', cable);
      console.log('ActionCable URL:', cable.url);
      console.log('Current user ID for ActionCable:', currentUserId);

      // Test basic connection
      const testSubscription = cable.subscriptions.create("PresenceChannel", {
        connected() {
          console.log("✅ ActionCable test connection successful - authentication working");
          testSubscription.unsubscribe();
        },
        disconnected() {
          console.log("ActionCable test disconnected");
        },
        rejected() {
          console.error("❌ ActionCable test connection REJECTED - authentication failed!");
          console.error("This means the ActionCable connection authentication is broken");
        }
      });
    } catch (error) {
      console.error('❌ ActionCable test failed:', error);
    }
  }

  // Load initial user list and unread counts
  function loadInitialUserList() {
    console.log('📋 Chat: Loading initial user list');

    // Mark current user as online and get user list (original working approach)
    fetch('/api/v1/chat/update_status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
      },
      credentials: 'same-origin',
      body: JSON.stringify({ online: true })
    })
    .then(response => response.json())
    .then(data => {
      console.log('📋 Chat: User list response:', data);
      if (data.success && data.users) {
        renderUsersList(data.users);

        // Load unread counts
        if (data.unread_counts) {
          unreadCounts = data.unread_counts;
          updateUserListDisplay();
        }
      } else {
        console.error('Error loading user list:', data.error || data);
      }
    })
    .catch(error => {
      console.error('Error loading user list:', error);

      // Show error message to user
      if (chatUsersList) {
        chatUsersList.innerHTML = `
          <div style="text-align: center; padding: 20px; color: #d32f2f;">
            <i class="icon-solid icon-warning" style="font-size: 24px; margin-bottom: 8px;"></i>
            <p>Error loading users</p>
            <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 10px; background: #1976d2; color: white; border: none; border-radius: 3px; cursor: pointer;">
              Retry
            </button>
          </div>
        `;
      }
    });
  }

  // Store all users for search functionality
  function renderUsersList(users, searchTerm = '') {

    console.log('-----indexjs renderUsersList users: ', users);
    console.log('-----indexjs renderUsersList searchTerm: ', searchTerm);

    if (!chatUsersList) {
      console.error('Chat users list element not found');
      return;
    }

    allUsers = users;
    const loadingElement = chatUsersList.querySelector('.loading-users');
    if (loadingElement) {
      loadingElement.remove();
    }
    
    // Clear existing users (except loading)
    const existingUsers = chatUsersList.querySelectorAll('.chat-user-item');
    existingUsers.forEach(user => user.remove());

    // Filter users based on search term
    const filteredUsers = users.filter(user => 
      user.id !== currentUserId && 
      (searchTerm === '' || user.name?.toLowerCase().includes(searchTerm?.toLowerCase()))
    );
    
    console.log('-----indexjs renderUsersList filteredUsers: ', filteredUsers);
    filteredUsers.forEach(userData => {
      // The data structure is now flat, not nested under 'user'
      const userDiv = document.createElement('div');
      userDiv.className = 'chat-user-item';
      userDiv.dataset.userId = userData.id;

      const avatarUrl = userData.avatar_image_url || '/images/messages/avatar-50.png';
      const statusText = userData.online ? 'Online' : 'Offline';

      userDiv.innerHTML = `
        <img src="${avatarUrl}" alt="${userData.name}" class="user-avatar" onerror="this.src='/images/messages/avatar-50.png'">
        <div class="user-info">
          <div class="user-name">${userData.name}</div>
          <div class="user-status-indicator">
            <div class="user-status ${userData.online ? 'online' : 'offline'}"></div>
            <span class="user-status-text">${statusText}</span>
          </div>
        </div>
        <span class="user-unread-badge" style="display: none;">
          <span class="unread-count-number">0</span>
        </span>
      `;

      userDiv.addEventListener('click', () => {
        selectUser(userData);
      });

      chatUsersList.appendChild(userDiv);
    });
    
    // Show "No users found" message if search returns no results
    if (filteredUsers.length === 0 && searchTerm) {
      const noResultsDiv = document.createElement('div');
      noResultsDiv.className = 'no-users-found';
      noResultsDiv.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #666;">
          <i class="icon-solid icon-search" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
          <p>No users found</p>
        </div>
      `;
      chatUsersList.appendChild(noResultsDiv);
    }
  }

  // Update user list display with notification indicators
  function updateUserListDisplay() {
    const userItems = chatUsersList.querySelectorAll('.chat-user-item');

    userItems.forEach(userItem => {
      const userId = parseInt(userItem.dataset.userId);
      const unreadBadge = userItem.querySelector('.user-unread-badge');
      const unreadCountElement = unreadBadge.querySelector('.unread-count-number');
      const unreadCount = unreadCounts[userId] || 0;

      if (unreadCount > 0) {
        // Show notification indicator with count and add unread styling
        unreadCountElement.textContent = unreadCount;
        unreadBadge.style.display = 'block';
        userItem.classList.add('has-unread');
      } else {
        // Hide notification indicator and remove unread styling
        unreadBadge.style.display = 'none';
        userItem.classList.remove('has-unread');
      }
    });
  }

  // Set up event listeners
  function setupEventListeners() {
    // Search functionality
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value;
        renderUsersList(allUsers, searchTerm);
      });
    }
    
    // Chat form submission
    if (chatForm) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        sendMessage();
      });
    }
    
    // Enter key to send message
    if (chatInput) {
      chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });
    }
  }

  // Select a user to chat with
  function selectUser(userData) {
    // Clear previous active state
    const previousActive = document.querySelector('.chat-user-item.active');
    if (previousActive) {
      previousActive.classList.remove('active');
    }

    // Set new active state
    const userDiv = document.querySelector(`[data-user-id="${userData.id}"]`);
    if (userDiv) {
      userDiv.classList.add('active');
    }

    // Update selected user info
    selectedUserName.textContent = userData.name;
    const selectedUserStatus = document.getElementById('selected-user-status');
    const selectedUserAvatar = document.getElementById('selected-user-avatar');

    if (selectedUserStatus) {
      selectedUserStatus.textContent = userData.online ? 'Online' : 'Offline';
    }

    if (selectedUserAvatar) {
      const avatarUrl = userData.avatar_image_url || '/images/messages/avatar-50.png';
      selectedUserAvatar.src = avatarUrl;
      selectedUserAvatar.style.display = 'block';
      selectedUserAvatar.onerror = function() { this.src = '/images/messages/avatar-50.png'; };
    }

    // Hide chat placeholder
    const chatPlaceholder = document.querySelector('.chat-placeholder');
    if (chatPlaceholder) {
      chatPlaceholder.style.display = 'none';
    }

    currentRecipientId = userData.id;
    recipientIdInput.value = userData.id;

    // Enable input and button
    chatInput.disabled = false;
    sendButton.disabled = false;

    // Clear unread count for this user
    unreadCounts[userData.id] = 0;
    updateUserListDisplay();



    // Load messages for this conversation
    fetchMessages(userData.id);

    // Mark any unread messages from this user as read
    markAsRead(userData.id);

    // Setup chat channel subscription for this conversation
    setupChatChannel(userData.id);
  }





  // Mark messages as read via ActionCable
  function markAsRead(senderId) {
    if (!senderId || !chatSubscription) return;

    // Send mark as read through ActionCable channel
    chatSubscription.perform('mark_as_read', {
      sender_id: senderId
    });


  }



  // Fetch messages for a conversation
  function fetchMessages(recipientId, shouldScrollToBottom = true) {
    fetch(`/api/v1/chat/messages?recipient_id=${recipientId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
      },
      credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
      if (data.messages) {
        renderMessages(data.messages, shouldScrollToBottom);

        // Messages loaded successfully

        // Clear unread count for this user since we're viewing their messages
        if (unreadCounts[recipientId]) {
          unreadCounts[recipientId] = 0;
          updateUserListDisplay();
        }
      }
    })
    .catch(error => {
      console.error('Error fetching messages:', error);
    });
  }

  // Render messages in the chat area
  function renderMessages(messages, shouldScrollToBottom = true) {
    const wasAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 5;

    chatMessages.innerHTML = '';

    messages.forEach(message => {
      addMessageToChat(message);
    });

    // Only scroll to bottom if user was already at bottom or if explicitly requested
    if (shouldScrollToBottom || wasAtBottom) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }

  // Get message status icon based on message state
  function getMessageStatusIcon(message) {
    // Ensure both IDs are compared as numbers for accurate comparison
    const messageUserId = parseInt(message.user_id);
    const currentUserIdNum = parseInt(currentUserId);
    const isSent = messageUserId === currentUserIdNum;

    // Only show status icons for sent messages
    if (!isSent) {
      return '';
    }

    // Determine status based on timestamps
    if (message.seen_at) {
      // Message has been seen - use eye icon
      return '<i class="icon-Line icon-eye message-status-icon seen" title="Seen" aria-label="Message seen"></i>';
    } else if (message.delivered_at) {
      // Message has been delivered - use check icon
      return '<i class="icon-Line icon-check message-status-icon delivered" title="Delivered" aria-label="Message delivered"></i>';
    } else {
      // Message has been sent but not delivered - use clock icon
      return '<i class="icon-Line icon-clock message-status-icon sent" title="Sent" aria-label="Message sent"></i>';
    }
  }

  // Add a single message to the chat
  function addMessageToChat(message) {
    const messageDiv = document.createElement('div');

    // Use the global currentUserId variable that was set at initialization
    // Ensure both IDs are compared as numbers for accurate comparison
    const messageUserId = parseInt(message.user_id);
    const currentUserIdNum = parseInt(currentUserId);
    const isSent = messageUserId === currentUserIdNum;



    const messageClass = `message ${isSent ? 'sent' : 'received'}`;
    messageDiv.className = messageClass;

    // Apply inline styles to ensure alignment works regardless of CSS conflicts
    if (isSent) {
      messageDiv.style.display = 'flex';
      messageDiv.style.flexDirection = 'column';
      messageDiv.style.alignItems = 'flex-end';
      messageDiv.style.textAlign = 'right';
      messageDiv.style.marginBottom = '16px';
    } else {
      messageDiv.style.display = 'flex';
      messageDiv.style.flexDirection = 'column';
      messageDiv.style.alignItems = 'flex-start';
      messageDiv.style.textAlign = 'left';
      messageDiv.style.marginBottom = '16px';
    }

    const messageTime = new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    const statusIcon = getMessageStatusIcon(message);

    messageDiv.innerHTML = `
      <div class="message-content" style="${isSent ?
        'background-color: #00b04f !important; color: white !important; margin-left: auto; margin-right: 0; max-width: 75%; padding: 12px 16px; border-radius: 18px; border-bottom-right-radius: 4px; display: inline-block;' :
        'background-color: #f1f1f1 !important; color: #333 !important; margin-left: 0; margin-right: auto; max-width: 75%; padding: 12px 16px; border-radius: 18px; border-bottom-left-radius: 4px; display: inline-block;'
      }">${message.body}</div>
      <div class="message-footer">
        <div class="message-time">${messageTime}</div>
        ${statusIcon}
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  // Send a message via ActionCable ONLY
  function sendMessage() {
    const messageText = chatInput.value.trim();
    if (!messageText || !currentRecipientId) {
      console.log('Cannot send message: missing text or recipient');
      return;
    }

    if (!chatSubscription) {
      console.error('❌ ChatSubscription not available! ActionCable connection failed.');
      alert('Chat connection not available. Please refresh the page.');
      return;
    }

    console.log('Sending message via ActionCable:', messageText);
    console.log('ChatSubscription object:', chatSubscription);

    try {
      // Send message through ActionCable channel
      chatSubscription.perform('speak', {
        message: messageText
      });

      // Clear input immediately
      chatInput.value = '';
      console.log('✅ Message sent via ActionCable');

    } catch (error) {
      console.error('❌ Error sending message via ActionCable:', error);
      alert('Failed to send message. Please check the console for errors.');
    }
  }



  // Setup chat channel for real-time messaging
  function setupChatChannel(recipientId) {
    console.log(`Setting up chat channel for recipient ${recipientId}`);

    // Disconnect existing chat subscription if any
    if (chatSubscription) {
      console.log('Unsubscribing from existing chat channel');
      chatSubscription.unsubscribe();
    }

    try {
      console.log('Creating ActionCable consumer...');
      const cable = createConsumer('/cable');

      if (!cable) {
        console.warn('ActionCable not available for chat');
        return;
      }

      console.log('Creating chat channel subscription...');
      chatSubscription = cable.subscriptions.create(
        { channel: "ChatChannel", recipient_id: recipientId },
        {
          connected() {
            console.log(`✅ Connected to chat channel with user ${recipientId}`);
          },

          disconnected() {
            console.log("❌ Disconnected from chat channel");
          },

          rejected() {
            console.error("❌ Chat channel subscription REJECTED!");
            console.error("This usually means ActionCable authentication failed");
            console.error("Check app/channels/application_cable/connection.rb");
            chatSubscription = null; // Clear the failed subscription
          },

          received(data) {
            console.log("Received chat data:", data);

            if (data.type === 'messages_read') {
              // Handle read status update
              handleMessageReadUpdate(data);
            } else if (data.type === 'new_message' && data.message) {
              // Handle new message
              const message = data.message;
              const messageUserId = parseInt(message.user_id);
              const currentUserIdNum = parseInt(currentUserId);

              console.log(`Message from user ${messageUserId}, current user ${currentUserIdNum}, current recipient ${currentRecipientId}`);

              // Only process messages that are relevant to this conversation
              if (messageUserId === currentUserIdNum || messageUserId === parseInt(currentRecipientId)) {
                // If this is a message from someone else (not current user)
                if (messageUserId !== currentUserIdNum) {
                  // If we're not currently viewing this user's chat, increment unread count
                  if (currentRecipientId !== messageUserId) {
                    console.log(`Incrementing unread count for user ${messageUserId}`);
                    unreadCounts[messageUserId] = (unreadCounts[messageUserId] || 0) + 1;
                    updateUserListDisplay();
                  }
                }

                // Add message to chat if we're viewing the relevant conversation
                if (currentRecipientId === messageUserId || messageUserId === currentUserIdNum) {
                  addMessageToChat(message);
                }
              }
            }
          }
        }
      );

      console.log('Chat subscription created:', chatSubscription);
      console.log('Available methods:', Object.getOwnPropertyNames(chatSubscription));

    } catch (error) {
      console.error('Failed to setup chat channel:', error);
    }
  }

  // Handle message read status updates
  function handleMessageReadUpdate(data) {
    console.log('Messages marked as read:', data);
    console.log('Current user ID:', currentUserId, 'type:', typeof currentUserId);
    console.log('Sender ID from data:', data.sender_id, 'type:', typeof data.sender_id);
    console.log('Reader ID from data:', data.reader_id, 'type:', typeof data.reader_id);

    // Convert to numbers for comparison
    const currentUserIdNum = parseInt(currentUserId);
    const senderIdNum = parseInt(data.sender_id);
    const readerIdNum = parseInt(data.reader_id);

    console.log('Comparison: currentUserIdNum === senderIdNum:', currentUserIdNum === senderIdNum);
    console.log('Comparison: readerIdNum !== currentUserIdNum:', readerIdNum !== currentUserIdNum);

    // If the current user's messages were read by someone else
    if (currentUserIdNum === senderIdNum && readerIdNum !== currentUserIdNum) {
      console.log('✅ Updating sent messages to show as seen');
      // Update UI to show messages as seen
      updateSentMessagesReadStatus();
    } else {
      console.log('❌ Not updating messages - conditions not met');
    }
  }

  // Update sent messages to show they've been read
  function updateSentMessagesReadStatus() {
    console.log('🔍 Looking for sent messages to update...');
    const sentMessages = chatMessages.querySelectorAll('.message.sent');
    console.log('Found sent messages:', sentMessages.length);

    sentMessages.forEach((messageEl, index) => {
      console.log(`Checking message ${index + 1}:`, messageEl);
      const statusIcon = messageEl.querySelector('.message-status-icon');
      console.log(`Status icon found:`, statusIcon);

      if (statusIcon) {
        console.log(`Current icon classes:`, statusIcon.className);
        console.log(`Has 'seen' class:`, statusIcon.classList.contains('seen'));

        if (!statusIcon.classList.contains('seen')) {
          console.log(`✅ Updating message ${index + 1} status to seen`);
          // Update to seen status
          statusIcon.className = 'icon-Line icon-eye message-status-icon seen';
          statusIcon.title = 'Seen';
          statusIcon.setAttribute('aria-label', 'Message seen');
          console.log(`Updated icon classes:`, statusIcon.className);
        } else {
          console.log(`⏭️ Message ${index + 1} already marked as seen`);
        }
      } else {
        console.log(`❌ No status icon found for message ${index + 1}`);
      }
    });
  }

  // Setup ActionCable presence channel for real-time status updates
  function setupPresenceChannel() {
    try {
      // Create ActionCable consumer
      const cable = createConsumer('/cable');

      if (!cable) {
        console.warn('ActionCable not available, falling back to polling only');
        return;
      }

      console.log('Setting up presence channel subscription (global status mode)...');

      cable.subscriptions.create("PresenceChannel", {
        connected() {
          console.log("✅ Connected to presence channel - status managed globally");
        },

        disconnected() {
          console.log("❌ Disconnected from presence channel - status managed globally");
        },

        received(data) {
          if (data.user_id && data.online !== undefined) {
            updateUserStatusInList(data.user_id, data.online);
          }
        }
      });

      // Setup global message notifications
      setupGlobalMessageNotifications(cable);

    } catch (error) {
      console.warn('Failed to setup ActionCable presence channel:', error);
      console.warn('Falling back to polling only');
    }
  }

  // Setup global message notifications for all users
  function setupGlobalMessageNotifications(cable) {
    // Subscribe to a global notification channel for this user
    cable.subscriptions.create(
      { channel: "GlobalNotificationChannel", user_id: currentUserId },
      {
        connected() {
          console.log("✅ Connected to global notification channel");
        },

        disconnected() {
          console.log("❌ Disconnected from global notification channel");
        },

        received(data) {
          if (data.type === 'new_message_notification') {
            const senderId = data.sender_id;
            const messageCount = data.message_count || 1;

            // Only increment if we're not currently viewing this user's chat
            if (currentRecipientId !== senderId) {
              console.log(`📢 New message notification from user ${senderId}`);
              unreadCounts[senderId] = (unreadCounts[senderId] || 0) + messageCount;
              updateUserListDisplay();
            }
          }
        }
      }
    );
  }

  // Update a specific user's status in the UI
  function updateUserStatusInList(userId, isOnline) {
    // Update in the users list
    const userDiv = document.querySelector(`[data-user-id="${userId}"]`);
    if (userDiv) {
      const statusIndicator = userDiv.querySelector('.user-status');
      const statusText = userDiv.querySelector('.user-status-text');

      if (statusIndicator) {
        statusIndicator.className = `user-status ${isOnline ? 'online' : 'offline'}`;
      }
      if (statusText) {
        statusText.textContent = isOnline ? 'Online' : 'Offline';
      }
    }

    // Update in the selected user area if this is the current conversation
    if (currentRecipientId && parseInt(currentRecipientId) === parseInt(userId)) {
      const selectedUserStatus = document.getElementById('selected-user-status');
      if (selectedUserStatus) {
        selectedUserStatus.textContent = isOnline ? 'Online' : 'Offline';
      }
    }

    // Update the allUsers array to keep it in sync
    const userIndex = allUsers.findIndex(u => parseInt(u.id) === parseInt(userId));
    if (userIndex !== -1) {
      allUsers[userIndex].online = isOnline;
    }
  }

  // Setup page unload handler - simplified since global status management handles this
  function setupPageUnloadHandler() {
    console.log('📄 Chat: Page unload handler setup (global status management active)');

    // Handle visibility change for chat-specific functionality
    document.addEventListener('visibilitychange', function() {
      if (document.visibilityState === 'visible') {
        console.log('👁️ Chat: Tab became visible - global status will handle reconnection');
      } else {
        console.log('🙈 Chat: Tab hidden - global status continues to manage online state');
      }
    });

    // Note: Online/offline status is now managed by global_online_status.js
    // This chat page no longer needs to manage user online status directly
  }

  // Make initialize function available globally for unified messages page
  window.initializeChatPage = initialize;

  // Auto-initialize if we're on a standalone chat page (elements are immediately available)
  if (document.querySelector('.chat-users-list')) {
    console.log('Chat elements found, auto-initializing...');
    initialize();
  } else {
    console.log('Chat elements not found, waiting for manual initialization...');
  }
})();
